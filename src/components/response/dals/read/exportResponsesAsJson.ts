import { responseModel } from '../../../response/models';
import { Var } from '../../../../global/var';

export const exportResponsesAsJson = async (surveyId: string, accountId: string, includeDeleted: boolean = false, whereConditions?: any) => {
  try {
    let finalWhereConditions: any;
    
    if (whereConditions) {
      // Use provided where conditions (which include time filtering)
      finalWhereConditions = { ...whereConditions };
      if (!includeDeleted) {
        finalWhereConditions.is_deleted = false;
      }
    } else {
      // Fallback to basic conditions
      finalWhereConditions = {
        survey_id: surveyId,
        account_id: accountId,
      };
      if (!includeDeleted) {
        finalWhereConditions.is_deleted = false;
      }
    }

    const responses = await responseModel.findAll({
      where: finalWhereConditions,
      order: [['created_at', 'DESC']],
    });

    if (responses.length === 0) {
      return {
        success: false,
        message: `${Var.app.emoji.warning} No responses found for this survey`,
        payload: null,
      };
    }

    // Process responses for JSON export
    const processedResponses = responses.map(response => {
      const responseData = response.dataValues;
      
      return {
        id: responseData.id,
        survey_id: responseData.survey_id,
        account_id: responseData.account_id,
        response_data: responseData.response_data,
        respondent_details: responseData.respondent_details,
        respondent_info: responseData.respondent_info,
        meta: responseData.meta,
        created_at: responseData.created_at,
        updated_at: responseData.updated_at,
        is_deleted: responseData.is_deleted,
        delete_reason: responseData.delete_reason,
      };
    });

    const jsonData = {
      export_metadata: {
        survey_id: surveyId,
        total_responses: processedResponses.length,
        exported_at: new Date().toISOString(),
        include_deleted: includeDeleted,
        format: 'json',
      },
      responses: processedResponses,
    };

    const jsonString = JSON.stringify(jsonData, null, 2);

    return {
      success: true,
      message: `${Var.app.emoji.success} Responses exported`,
      payload: {
        filename: `responses_${surveyId}_${new Date().toISOString()}.json`,
        data: jsonString,
      },
    };
  } catch (error) {
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not export responses as JSON`,
      payload: error,
    };
  }
};
