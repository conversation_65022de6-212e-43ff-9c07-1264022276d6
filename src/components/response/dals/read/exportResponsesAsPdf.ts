import PDFDocument from 'pdfkit';
import { responseModel } from '../../../response/models';
import { Var } from '../../../../global/var';

export const exportResponsesAsPdf = async (surveyId: string, accountId: string, includeDeleted: boolean = false, whereConditions?: any) => {
  try {
    let finalWhereConditions: any;
    
    if (whereConditions) {
      // Use provided where conditions (which include time filtering)
      finalWhereConditions = { ...whereConditions };
      if (!includeDeleted) {
        finalWhereConditions.is_deleted = false;
      }
    } else {
      // Fallback to basic conditions
      finalWhereConditions = {
        survey_id: surveyId,
        account_id: accountId,
      };
      if (!includeDeleted) {
        finalWhereConditions.is_deleted = false;
      }
    }

    const responses = await responseModel.findAll({
      where: finalWhereConditions,
      order: [['created_at', 'DESC']],
    });

    if (responses.length === 0) {
      return {
        success: false,
        message: `${Var.app.emoji.warning} No responses found for this survey`,
        payload: null,
      };
    }

    // Create PDF document
    const doc = new PDFDocument({ margin: 50 });
    const chunks: Buffer[] = [];

    // Collect PDF data
    doc.on('data', (chunk) => chunks.push(chunk));
    
    return new Promise((resolve) => {
      doc.on('end', () => {
        const pdfBuffer = Buffer.concat(chunks);
        resolve({
          success: true,
          message: `${Var.app.emoji.success} Responses exported`,
          payload: {
            filename: `responses_${surveyId}_${new Date().toISOString()}.pdf`,
            data: pdfBuffer,
          },
        });
      });

      // Add title
      doc.fontSize(20).text('Survey Responses Export', { align: 'center' });
      doc.moveDown();

      // Add metadata
      doc.fontSize(12)
         .text(`Survey ID: ${surveyId}`)
         .text(`Total Responses: ${responses.length}`)
         .text(`Exported At: ${new Date().toISOString()}`)
         .text(`Include Deleted: ${includeDeleted ? 'Yes' : 'No'}`);
      
      doc.moveDown();

      // Add responses
      responses.forEach((response, index) => {
        const responseData = response.dataValues;
        
        // Check if we need a new page
        if (doc.y > 700) {
          doc.addPage();
        }

        doc.fontSize(14).text(`Response #${index + 1}`, { underline: true });
        doc.moveDown(0.5);

        doc.fontSize(10)
           .text(`Created: ${responseData.created_at}`)
           .text(`Deleted: ${responseData.is_deleted ? 'Yes' : 'No'}`);

        if (responseData.is_deleted && responseData.delete_reason) {
          doc.text(`Delete Reason: ${responseData.delete_reason}`);
        }

        // Add response data
        if (responseData.response_data) {
          doc.moveDown(0.5);
          doc.fontSize(12).text('Response Data:', { underline: true });
          doc.fontSize(10).text(JSON.stringify(responseData.response_data, null, 2));
        }

        // Add respondent details
        if (responseData.respondent_details) {
          doc.moveDown(0.5);
          doc.fontSize(12).text('Respondent Details:', { underline: true });
          doc.fontSize(10).text(JSON.stringify(responseData.respondent_details, null, 2));
        }

        // Add respondent info
        if (responseData.respondent_info) {
          doc.moveDown(0.5);
          doc.fontSize(12).text('Respondent Info:', { underline: true });
          doc.fontSize(10).text(JSON.stringify(responseData.respondent_info, null, 2));
        }

        // Add meta information
        if (responseData.meta) {
          doc.moveDown(0.5);
          doc.fontSize(12).text('Meta Information:', { underline: true });
          doc.fontSize(10).text(JSON.stringify(responseData.meta, null, 2));
        }

        doc.moveDown();
        doc.strokeColor('#cccccc').lineWidth(1).moveTo(50, doc.y).lineTo(550, doc.y).stroke();
        doc.moveDown();
      });

      // Finalize the PDF
      doc.end();
    });

  } catch (error) {
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not export responses as PDF`,
      payload: error,
    };
  }
};
