import { Request, Response } from 'express';

import { fetchResponsesWithConditions } from '../dals';
import { verifyResourceOwnership } from '../../security/helpers';
import { processTimeFilter } from '../helpers/analyticsHelpers';
import { logger } from '../../../global/services';

export const getExportController = async (req: Request, res: Response) => {
  const surveyId = res.locals.surveyId;
  const accountId = res.locals.accountId;

  // Extract query parameters for filtering
  const timeFilter = req.query.timeFilter as string;
  const customStartDate = req.query.customStartDate as string;
  const customEndDate = req.query.customEndDate as string;

  logger.info('Export data request', {
    surveyId: surveyId.substring(0, 8) + '...',
    accountId: accountId.substring(0, 8) + '...',
    filters: { timeFilter, customStartDate, customEndDate },
    ip: req.ip,
  });

  // SECURITY FIX: Double authorization check - middleware + controller level
  const isOwner = await verifyResourceOwnership('survey', surveyId, accountId);
  if (!isOwner) {
    logger.warn('Unauthorized export access attempt', {
      surveyId: surveyId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    return res.status(403).json({
      success: false,
      message: 'Access denied',
    });
  }

  // Process time filter and generate where conditions
  const { whereConditions } = processTimeFilter(surveyId, accountId, timeFilter, customStartDate, customEndDate);

  // Fetch responses count for export metadata (without pagination to get total count)
  const responsesResult = await fetchResponsesWithConditions(whereConditions, 1, 1);

  if (!responsesResult.success) {
    return res.status(400).json({
      success: false,
      message: responsesResult.message,
    });
  }

  const responsesData = responsesResult.payload as any;
  const totalResponses = responsesData.totalCount;

  // Prepare export metadata
  const exportPayload = {
    totalResponses,
    availableFormats: ['csv', 'json', 'pdf'],
    timeFilter: timeFilter || 'all-time',
    customStartDate,
    customEndDate,
    exportUrls: {
      csv: `/api/v1/surveys/${surveyId}/responses/export?format=csv${timeFilter ? `&timeFilter=${timeFilter}` : ''}${customStartDate ? `&customStartDate=${customStartDate}` : ''}${customEndDate ? `&customEndDate=${customEndDate}` : ''}`,
      json: `/api/v1/surveys/${surveyId}/responses/export?format=json${timeFilter ? `&timeFilter=${timeFilter}` : ''}${customStartDate ? `&customStartDate=${customStartDate}` : ''}${customEndDate ? `&customEndDate=${customEndDate}` : ''}`,
      pdf: `/api/v1/surveys/${surveyId}/responses/export?format=pdf${timeFilter ? `&timeFilter=${timeFilter}` : ''}${customStartDate ? `&customStartDate=${customStartDate}` : ''}${customEndDate ? `&customEndDate=${customEndDate}` : ''}`,
    },
    estimatedFileSizes: {
      csv: `${Math.ceil(totalResponses * 0.5)}KB`, // Rough estimate
      json: `${Math.ceil(totalResponses * 1.2)}KB`, // JSON is typically larger
      pdf: `${Math.ceil(totalResponses * 2)}KB`, // PDF with formatting
    },
  };

  logger.info('Export data retrieved successfully', {
    surveyId: surveyId.substring(0, 8) + '...',
    accountId: accountId.substring(0, 8) + '...',
    totalResponses,
    timeFilter: exportPayload.timeFilter,
    ip: req.ip,
  });

  return res.status(200).json({
    success: true,
    message: 'Export data retrieved successfully',
    payload: exportPayload,
  });
};
