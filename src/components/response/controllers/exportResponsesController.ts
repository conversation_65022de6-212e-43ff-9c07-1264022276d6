import { Request, Response } from 'express';
import { exportResponsesAsCsv, exportResponsesAsJson, exportResponsesAsPdf } from '../dals';
import { verifyResourceOwnership } from '../../security/helpers';
import { processTimeFilter } from '../helpers/analyticsHelpers';
import { logger } from '../../../global/services';

export const exportResponsesController = async (req: Request, res: Response) => {
  const surveyId = res.locals.surveyId;
  const accountId = res.locals.accountId;
  const includeDeleted = req.query.includeDeleted === 'true';
  const format = (req.query.format as string) || 'csv';

  // Extract time filter parameters
  const timeFilter = req.query.timeFilter as string;
  const customStartDate = req.query.customStartDate as string;
  const customEndDate = req.query.customEndDate as string;

  logger.info('Response export attempt', {
    surveyId: surveyId.substring(0, 8) + '...',
    accountId: accountId.substring(0, 8) + '...',
    includeDeleted,
    format,
    filters: { timeFilter, customStartDate, customEndDate },
    ip: req.ip,
  });

  // SECURITY FIX: Double authorization check - middleware + controller level
  const isOwner = await verifyResourceOwnership('survey', surveyId, accountId);
  if (!isOwner) {
    logger.warn('Unauthorized response export attempt', {
      surveyId: surveyId.substring(0, 8) + '...',
      accountId: accountId.substring(0, 8) + '...',
      ip: req.ip,
      userAgent: req.headers['user-agent'],
    });
    return res.status(403).json({
      success: false,
      message: 'Access denied',
    });
  }

  // Validate format
  const supportedFormats = ['csv', 'json', 'pdf'];
  if (!supportedFormats.includes(format)) {
    return res.status(400).json({
      success: false,
      message: `Unsupported format. Supported formats: ${supportedFormats.join(', ')}`,
    });
  }

  // Process time filter to get where conditions
  const { whereConditions } = processTimeFilter(surveyId, accountId, timeFilter, customStartDate, customEndDate);

  let result: any;
  let contentType: string;
  let fileExtension: string;

  // Export based on format
  switch (format) {
    case 'csv':
      result = await exportResponsesAsCsv(surveyId, accountId, includeDeleted, whereConditions);
      contentType = 'text/csv';
      fileExtension = 'csv';
      break;
    case 'json':
      result = await exportResponsesAsJson(surveyId, accountId, includeDeleted, whereConditions);
      contentType = 'application/json';
      fileExtension = 'json';
      break;
    case 'pdf':
      result = await exportResponsesAsPdf(surveyId, accountId, includeDeleted, whereConditions);
      contentType = 'application/pdf';
      fileExtension = 'pdf';
      break;
    default:
      return res.status(400).json({
        success: false,
        message: 'Invalid export format',
      });
  }

  if (!result.success) {
    return res.status(400).json({
      success: false,
      message: result.message,
    });
  }

  // Set headers for file download
  res.setHeader('Content-Type', contentType);
  res.setHeader('Content-Disposition', `attachment; filename="${result.payload.filename}"`);

  return res.status(200).send(result.payload.data);
};
